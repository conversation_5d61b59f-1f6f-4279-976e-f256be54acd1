name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.10, 3.11, 3.12]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install Java (for ThetaData Terminal)
      uses: actions/setup-java@v3
      with:
        distribution: 'temurin'
        java-version: '11'

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"

    - name: Lint with flake8
      run: |
        flake8 app tests --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 app tests --count --exit-zero --max-complexity=10 --max-line-length=88 --statistics

    - name: Check code formatting with black
      run: |
        black --check app tests

    - name: Check import sorting with isort
      run: |
        isort --check-only app tests

    - name: Type check with mypy
      run: |
        mypy app

    - name: Test with pytest
      run: |
        pytest --cov=app --cov-report=xml

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  docker:
    runs-on: ubuntu-latest
    needs: test

    steps:
    - uses: actions/checkout@v4

    - name: Build Docker image
      run: |
        docker build -t thetadata-api:latest .

    - name: Test Docker image
      run: |
        # Create a minimal .env file for testing
        echo "THETA_JAR_PATH=./ThetaTerminal.jar" > .env
        echo "THETA_USERNAME=test" >> .env
        echo "THETA_PASSWORD=test" >> .env
        
        # Run container in background
        docker run -d --name test-api -p 8000:8000 --env-file .env thetadata-api:latest
        
        # Wait for container to start
        sleep 10
        
        # Test health endpoint
        curl -f http://localhost:8000/api/v1/health || exit 1
        
        # Clean up
        docker stop test-api
        docker rm test-api
