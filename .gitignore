# Environments
.env
.env.*
.venv
env/
venv/

# Python
__pycache__/
*.pyc
*.pyo
*.pyd
*.pyw
*.pyz

# FastAPI specific
*.db
*.sqlite
*.sqlite3

# ThetaData specific files
ThetaTerminal.log*
theta_data_credentials.json
theta_config.json
theta_auth.json
*.theta
theta_cache/
data/
logs/
*.csv
*.parquet
*.h5
*.hdf5

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Log files
*.log
logs/
log/

# JetBrains IDEs
.idea/
*.iml
*.iws
*.ipr

# Visual Studio Code
.vscode/

# Docker
.dockerignore
Dockerfile.dev

# Local development
local/
development/
dev/
