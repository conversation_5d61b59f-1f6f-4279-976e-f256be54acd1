# Trading API Architecture - Migration Plan

## Current Working State
- ✅ FastAPI app with historical data and options chain endpoints
- ✅ ThetaData Terminal integration working
- ✅ Health checks and terminal management endpoints
- ✅ Environment-based configuration

## Target Modular Architecture

```
/home/<USER>/trade/api/
├── main.py                 # FastAPI app entry point only
├── config.py              # Keep as-is
├── requirements.txt       # Keep as-is  
├── README.md              # Keep as-is
├── run.py                 # Keep as-is
├── ThetaTerminal.jar      # Keep as-is
├── env_template.txt       # Keep as-is
├── .gitignore            # Keep as-is
│
├── app/                   # New application package
│   ├── __init__.py
│   │
│   ├── api/               # API route handlers
│   │   ├── __init__.py
│   │   ├── health.py      # Health check endpoints
│   │   ├── market_data.py # Historical data + options chain
│   │   ├── theta.py       # ThetaData management endpoints
│   │   │
│   │   └── backtesting.py # NEW: Backtesting endpoints (future)
│   │
│   ├── services/          # Business logic services  
│   │   ├── __init__.py
│   │   └── theta_service.py # Move from root, keep all functionality
│   │
│   ├── models/            # Pydantic models
│   │   ├── __init__.py
│   │   ├── market_data.py # Historical data + options models
│   │   └── backtesting.py # NEW: Backtest models (future)
│   │
│   └── core/              # Core utilities (future expansion)
│       ├── __init__.py
│       └── utils.py       # Common utilities
│
└── tests/                 # Future: organized tests
    └── __init__.py
```

## Migration Mapping - Current Working Code

### 1. main.py → Modular Structure

**Current main.py sections:**
```python
# KEEP IN main.py:
- FastAPI app creation
- CORS middleware
- Lifespan events (startup/shutdown)
- App include_router() calls

# MOVE TO app/api/health.py:
@app.get("/health")
async def health_check()

# MOVE TO app/api/market_data.py:
@app.post("/historical-data", response_model=HistoricalDataResponse)
@app.post("/options-chain", response_model=OptionsChainResponse)  
@app.get("/historical-data-query", response_model=HistoricalDataResponse)

# MOVE TO app/api/theta.py:
@app.post("/theta/start")
@app.post("/theta/stop") 
@app.get("/theta/status")

# MOVE TO app/models/market_data.py:
class HistoricalDataRequest(BaseModel)
class HistoricalDataResponse(BaseModel)
class OptionsChainRequest(BaseModel)
class OptionsChainResponse(BaseModel)
class OptionData(BaseModel)
```

### 2. theta_service.py → app/services/theta_service.py

**Keep ALL existing functionality:**
- `ThetaDataService` class
- All existing methods
- Process management
- API calls to ThetaData
- Error handling
- Logging

**Only change:** Update import paths in other files

### 3. New main.py Structure

```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from app.services.theta_service import ThetaDataService
from app.api import health, market_data, theta
from config import get_settings

settings = get_settings()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    theta_service = ThetaDataService()
    app.state.theta_service = theta_service
    
    if settings.THETA_AUTO_START:
        await theta_service.start_terminal()
    
    yield
    
    # Shutdown  
    await theta_service.stop_terminal()

app = FastAPI(
    title="Trading API",
    description="API for historical market data and options chain data using ThetaData Terminal",
    version="1.0.0",
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health.router)
app.include_router(market_data.router)
app.include_router(theta.router)
```

### 4. New Router Files

**app/api/health.py:**
```python
from fastapi import APIRouter, Request
from typing import Dict, Any

router = APIRouter(tags=["health"])

@router.get("/health")
async def health_check(request: Request) -> Dict[str, Any]:
    # Move existing health check logic here
    # Access theta_service via: request.app.state.theta_service
```

**app/api/market_data.py:**
```python
from fastapi import APIRouter, Request, HTTPException
from app.models.market_data import (
    HistoricalDataRequest, HistoricalDataResponse,
    OptionsChainRequest, OptionsChainResponse
)

router = APIRouter(tags=["market_data"])

@router.post("/historical-data", response_model=HistoricalDataResponse)
async def get_historical_data(request: HistoricalDataRequest, req: Request):
    # Move existing historical data logic here
    
@router.post("/options-chain", response_model=OptionsChainResponse)
async def get_options_chain(request: OptionsChainRequest, req: Request):
    # Move existing options chain logic here

@router.get("/historical-data-query", response_model=HistoricalDataResponse)  
async def get_historical_data_query(...):
    # Move existing query endpoint logic here
```

**app/api/theta.py:**
```python
from fastapi import APIRouter, Request
from typing import Dict, Any

router = APIRouter(prefix="/theta", tags=["theta"])

@router.post("/start")
async def start_terminal(request: Request) -> Dict[str, Any]:
    # Move existing start logic here
    
@router.post("/stop")
async def stop_terminal(request: Request) -> Dict[str, Any]:
    # Move existing stop logic here
    
@router.get("/status")
async def get_status(request: Request) -> Dict[str, Any]:
    # Move existing status logic here
```

**app/models/market_data.py:**
```python
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

# Move all existing model classes here:
# - HistoricalDataRequest
# - HistoricalDataResponse  
# - OptionsChainRequest
# - OptionsChainResponse
# - OptionData
```

## Migration Steps

### Step 1: Create Directory Structure
```bash
mkdir -p app/api app/services app/models app/core tests
touch app/__init__.py app/api/__init__.py app/services/__init__.py 
touch app/models/__init__.py app/core/__init__.py tests/__init__.py
```

### Step 2: Move Files
```bash
mv theta_service.py app/services/
```

### Step 3: Extract Code from main.py
- Create each router file with extracted endpoints
- Create models file with extracted Pydantic models
- Update main.py to use routers

### Step 4: Update Imports
- Fix all import statements
- Test that everything still works

### Step 5: Add Backtesting Foundation
- Create `app/models/backtesting.py`
- Create `app/api/backtesting.py` 
- Add placeholder endpoints

## Critical Requirements

1. **Zero Functionality Loss**: All current endpoints must work exactly the same
2. **Keep ThetaData Integration**: Don't change theta_service.py logic
3. **Preserve Configuration**: config.py stays the same
4. **Maintain Startup/Shutdown**: ThetaData Terminal lifecycle unchanged

## Future Expansion Points

Once migrated, adding new features becomes:

**For Backtesting:**
1. Add models in `app/models/backtesting.py`
2. Add service in `app/services/backtesting_service.py`
3. Add routes in `app/api/backtesting.py`
4. Include router in main.py

**For Strategies, Analytics, Trading:**
- Follow same pattern
- Each gets its own models, services, and API files
- Clean separation of concerns

## Testing Strategy

After each migration step:
1. ✅ `python run.py` starts successfully
2. ✅ `/health` endpoint works
3. ✅ `/historical-data` endpoint works  
4. ✅ `/options-chain` endpoint works
5. ✅ `/theta/*` endpoints work
6. ✅ ThetaData Terminal starts/stops correctly

Ready to proceed with implementation? 