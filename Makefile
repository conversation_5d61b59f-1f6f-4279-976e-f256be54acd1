.PHONY: help install install-dev test test-unit test-integration test-e2e lint format clean run docker-build docker-run

# Default target
help:
	@echo "Available commands:"
	@echo "  install       Install production dependencies"
	@echo "  install-dev   Install development dependencies"
	@echo "  test          Run all tests"
	@echo "  test-unit     Run unit tests only"
	@echo "  test-integration  Run integration tests only"
	@echo "  test-e2e      Run end-to-end tests only"
	@echo "  lint          Run linting checks"
	@echo "  format        Format code with black and isort"
	@echo "  clean         Clean up cache files"
	@echo "  run           Run the development server"
	@echo "  docker-build  Build Docker image"
	@echo "  docker-run    Run Docker container"

# Installation
install:
	pip install -e .

install-dev:
	pip install -e ".[dev]"
	pre-commit install

# Testing
test:
	pytest

test-unit:
	pytest tests/unit -m unit

test-integration:
	pytest tests/integration -m integration

test-e2e:
	pytest tests/e2e -m e2e

# Code quality
lint:
	flake8 app tests
	mypy app
	black --check app tests
	isort --check-only app tests

format:
	black app tests
	isort app tests

# Cleanup
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/

# Development
run:
	python main.py

# Docker
docker-build:
	docker build -t thetadata-api .

docker-run:
	docker run -p 8000:8000 --env-file .env thetadata-api
