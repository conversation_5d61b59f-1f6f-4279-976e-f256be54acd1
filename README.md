# ThetaData Historical API

A FastAPI-based REST API for retrieving historical market data and options chain data via ThetaData Terminal.

## Features

- 📈 **Historical Data API**: Retrieve historical market data with multiple time scales
- 📊 **Options Chain API**: Get options chain data for specific strikes and dates
- 🚀 **Fast and Async**: High-performance async API built with FastAPI
- 🔧 **ThetaData Terminal Management**: Automatic start/stop/status management
- 📝 **Comprehensive Documentation**: Swagger UI and ReDoc integration
- 🧪 **Full Test Suite**: Unit, integration, and e2e tests with pytest
- 🔍 **Code Quality**: Black, isort, flake8, mypy integration
- 🐳 **Docker Support**: Easy deployment with Docker
- 📋 **Type Safety**: Pydantic models for request/response validation

## Prerequisites

- Python 3.10+
- ThetaData Terminal JAR file
- ThetaData account credentials
- Java 11+ (for ThetaData Terminal)

## Quick Start

### 1. Installation

Clone the repository and set up the environment:

```bash
git clone <repository-url>
cd api

# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -e .
# Or for development with all tools:
pip install -e ".[dev]"
```

### 2. Environment Configuration

Create a `.env` file from the template:

```bash
cp env_template.txt .env
```

Edit `.env` with your ThetaData credentials:

```bash
# ThetaData Configuration
THETA_USERNAME=your_username
THETA_PASSWORD=your_password

# Optional: Customize paths and ports
THETA_JAR_PATH=./ThetaTerminal.jar
THETA_HOST=localhost
THETA_PORT=25510

# API Configuration
HOST=0.0.0.0
PORT=8000
RELOAD=true
LOG_LEVEL=info
```

### 3. Run the Application

```bash
# Using the main entry point (recommended)
python main.py

# Or directly with uvicorn
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 4. Access the API

- **API Documentation**: http://localhost:8000/docs
- **Interactive API**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health

## API Endpoints

### System Endpoints

**GET** `/health` - Health check with ThetaData Terminal status
**GET** `/theta/status` - Get ThetaData Terminal connection status  
**POST** `/theta/start` - Start ThetaData Terminal process
**POST** `/theta/stop` - Stop ThetaData Terminal process

### Historical Data

**POST** `/historical-data`

Retrieve historical market data for a given asset within a date range and time scale.

```json
{
  "asset": "AAPL",
  "start_date": "2024-01-01", 
  "end_date": "2024-01-31",
  "time_scale": "1d"
}
```

**GET** `/historical-data-query`

Alternative GET endpoint using query parameters:
```
GET /historical-data-query?asset=AAPL&start_date=2024-01-01&end_date=2024-01-31&time_scale=1d
```

#### Supported Time Scales
- `1s`, `5s`, `10s`, `30s` - Seconds
- `1m`, `5m`, `15m`, `30m` - Minutes  
- `1h` - Hour
- `1d` - Day

### Options Chain Data

**POST** `/options-chain`

Retrieve historical options chain data for a given asset at a specific time and strike prices.

```json
{
  "asset": "SPY",
  "datetime": "2024-01-15T15:30:00",
  "strikes": [450.0, 455.0, 460.0],
  "expiration_date": "2024-01-19"
}
```

## Response Format

### Historical Data Response

```json
{
  "asset": "AAPL",
  "time_scale": "1d", 
  "data": [
    {
      "timestamp": "2024-01-01T00:00:00",
      "open": 150.0,
      "high": 155.0,
      "low": 149.0,
      "close": 153.0,
      "volume": 1000000
    }
  ],
  "total_records": 1
}
```

### Options Chain Response

```json
{
  "asset": "SPY",
  "datetime": "2024-01-15T15:30:00",
  "options": [
    {
      "strike": 450.0,
      "expiration": "2024-01-19",
      "call_bid": 22.5,
      "call_ask": 23.0,
      "call_last": 22.75,
      "call_volume": 100,
      "call_open_interest": 500,
      "put_bid": 18.0,
      "put_ask": 18.25,
      "put_last": 18.1,
      "put_volume": 75,
      "put_open_interest": 300
    }
  ],
  "total_records": 1
}
```

## Development

### Project Structure

```
api/
├── app/                    # Application package
│   ├── api/               # API routes
│   │   └── api_v1/        # API version 1
│   │       ├── endpoints/ # Individual endpoint modules
│   │       │   ├── health.py
│   │       │   ├── historical_data.py
│   │       │   ├── options_chain.py
│   │       │   └── theta_control.py
│   │       └── api.py     # Main API router
│   ├── core/              # Core application components
│   │   ├── config.py      # Configuration settings
│   │   └── events.py      # FastAPI event handlers
│   ├── db/                # Database components
│   │   ├── __init__.py
│   │   └── database.py    # Database handler
│   ├── models/            # Pydantic models
│   │   ├── enums.py       # Enumeration types
│   │   ├── requests.py    # Request models
│   │   └── responses.py   # Response models
│   ├── services/          # Business logic services
│   │   └── theta_service.py # ThetaData integration
│   └── main.py            # FastAPI application factory
├── tests/                 # Test suite
│   ├── unit/              # Unit tests
│   ├── integration/       # Integration tests
│   └── e2e/               # End-to-end tests
├── docs/                  # Documentation
├── scripts/               # Utility scripts
├── main.py                # Application entry point
├── pyproject.toml         # Project configuration
├── requirements.txt       # Python dependencies
├── env_template.txt       # Environment variables template
├── ThetaTerminal.jar      # ThetaData Terminal (required)
├── Dockerfile             # Docker configuration
├── docker-compose.yml     # Docker Compose setup
└── README.md              # This file
```

### ThetaData Integration

The application automatically integrates with ThetaData Terminal using the provided JAR file:

#### **Automatic Terminal Management**
- **Auto-start**: Terminal starts automatically with credentials from environment
- **Health monitoring**: Continuous health checks ensure terminal availability  
- **Error handling**: Graceful fallbacks when terminal is unavailable
- **Process management**: Clean startup/shutdown of Java process

#### **Real API Integration**
- **HTTP Client**: Uses requests with retry logic and connection pooling
- **Authentication**: Handles ThetaData username/password authentication
- **Rate limiting**: Built-in respect for ThetaData API limits
- **Data transformation**: Converts ThetaData responses to standardized format

#### **Endpoints Used**
- `GET /health` - Terminal health check
- `GET /historical/{symbol}` - Historical market data
- `GET /options/chain/{symbol}` - Options chain data
- WebSocket streaming (future enhancement)

#### **Configuration**
All ThetaData settings are managed through environment variables in your `.env` file. The service automatically validates configuration on startup.

### Running in Production

For production deployment:

1. **Set environment variables**:
   ```bash
   export HOST=0.0.0.0
   export PORT=8000
   export RELOAD=false
   export LOG_LEVEL=warning
   ```

2. **Use a production ASGI server**:
   ```bash
   gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker
   ```

3. **Add authentication middleware** if needed
4. **Configure reverse proxy** (nginx, traefik, etc.)

## Requirements

- Python 3.8+
- ThetaData Terminal JAR file
- Valid ThetaData credentials
- All dependencies in `requirements.txt`

## License

[Your License Here]
