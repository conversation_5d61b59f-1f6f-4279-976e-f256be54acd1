# ThetaData Historical API

A FastAPI-based REST API for retrieving historical market data and options chain data via ThetaData Terminal.

## Features

- 📈 Historical market data retrieval with multiple time scales
- 📊 Options chain data access
- 🚀 Fast and async API built with FastAPI
- 🔧 ThetaData Terminal management (start/stop/status)
- 📝 Comprehensive API documentation with Swagger UI
- 🐳 Docker support for easy deployment
- 🧪 Full test suite with pytest
- 🔍 Code quality tools (black, isort, flake8, mypy)

## Quick Start

### Prerequisites

- Python 3.10+
- ThetaData Terminal JAR file
- ThetaData account credentials

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd api
```

2. Create and activate a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
make install-dev
```

4. Set up environment variables:
```bash
cp env_template.txt .env
# Edit .env with your ThetaData credentials
```

5. Run the development server:
```bash
make run
```

The API will be available at:
- API: http://localhost:8000
- Documentation: http://localhost:8000/docs
- Alternative docs: http://localhost:8000/redoc

## Project Structure

```
api/
├── app/                    # Application package
│   ├── api/               # API routes
│   │   └── api_v1/        # API version 1
│   │       ├── endpoints/ # Individual endpoint modules
│   │       └── api.py     # Main API router
│   ├── core/              # Core application components
│   │   ├── config.py      # Configuration settings
│   │   └── events.py      # FastAPI event handlers
│   ├── models/            # Pydantic models
│   │   ├── enums.py       # Enumeration types
│   │   ├── requests.py    # Request models
│   │   └── responses.py   # Response models
│   ├── services/          # Business logic services
│   │   └── theta_service.py # ThetaData integration
│   └── main.py            # FastAPI application factory
├── tests/                 # Test suite
│   ├── unit/              # Unit tests
│   ├── integration/       # Integration tests
│   └── e2e/               # End-to-end tests
├── docs/                  # Documentation
├── scripts/               # Utility scripts
├── docker-compose.yml     # Docker Compose configuration
├── Dockerfile             # Docker image definition
├── Makefile              # Development commands
├── pyproject.toml        # Project configuration
└── requirements.txt      # Python dependencies
```

## API Endpoints

### Health & Status
- `GET /api/v1/` - Root endpoint
- `GET /api/v1/health` - Health check
- `GET /api/v1/theta/status` - ThetaData Terminal status
- `POST /api/v1/theta/start` - Start ThetaData Terminal
- `POST /api/v1/theta/stop` - Stop ThetaData Terminal

### Data Endpoints
- `POST /api/v1/historical-data/` - Get historical market data
- `GET /api/v1/historical-data/query` - Get historical data via query params
- `POST /api/v1/options-chain/` - Get options chain data

## Development

### Available Commands

```bash
make help              # Show all available commands
make install          # Install production dependencies
make install-dev      # Install development dependencies
make test             # Run all tests
make test-unit        # Run unit tests only
make lint             # Run linting checks
make format           # Format code
make clean            # Clean up cache files
make run              # Run development server
```

### Code Quality

This project uses several tools to maintain code quality:

- **Black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting
- **mypy**: Type checking
- **pre-commit**: Git hooks for quality checks

### Testing

Run tests with different scopes:

```bash
make test              # All tests
make test-unit         # Unit tests only
make test-integration  # Integration tests only
make test-e2e          # End-to-end tests only
```

## Docker Deployment

### Build and run with Docker:

```bash
make docker-build
make docker-run
```

### Or use Docker Compose:

```bash
docker-compose up -d
```

## Configuration

Configuration is managed through environment variables. See `env_template.txt` for all available options.

Key settings:
- `THETA_JAR_PATH`: Path to ThetaData Terminal JAR file
- `THETA_USERNAME`: ThetaData username
- `THETA_PASSWORD`: ThetaData password
- `HOST`: API server host (default: 0.0.0.0)
- `PORT`: API server port (default: 8000)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
