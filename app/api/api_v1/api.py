"""
Main API router that includes all endpoint routers
"""

from fastapi import APIRouter

from app.api.api_v1.endpoints import (
    health,
    historical_data,
    options_chain,
    theta_control,
)

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(health.router, tags=["health"])
api_router.include_router(theta_control.router, prefix="/theta", tags=["theta-control"])
api_router.include_router(
    historical_data.router, prefix="/historical-data", tags=["historical-data"]
)
api_router.include_router(
    options_chain.router, prefix="/options-chain", tags=["options-chain"]
)
