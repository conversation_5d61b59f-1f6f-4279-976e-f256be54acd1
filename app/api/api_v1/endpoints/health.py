"""
Health check endpoints
"""

from datetime import datetime

from fastapi import APIRouter

from app.models.responses import HealthResponse, MessageResponse
from app.services.theta_service import get_theta_service

router = APIRouter()


@router.get("/", response_model=MessageResponse)
async def root():
    """Root endpoint."""
    return MessageResponse(status="running", message="ThetaData Historical API")


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    theta_service = get_theta_service()
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now(),
        theta_terminal_running=theta_service.is_terminal_running(),
    )
