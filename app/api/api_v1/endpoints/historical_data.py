"""
Historical data endpoints
"""

from datetime import datetime

from fastapi import APIRouter, HTTPException, Query

from app.models.enums import TimeScale
from app.models.requests import HistoricalDataRequest
from app.models.responses import HistoricalDataPoint, HistoricalDataResponse
from app.services.theta_service import get_theta_service

router = APIRouter()


@router.post("/", response_model=HistoricalDataResponse)
async def get_historical_data(request: HistoricalDataRequest):
    """
    Retrieve historical market data for a given asset within a date range and time scale.

    - **asset**: Stock symbol (e.g., AAPL, SPY, TSLA)
    - **start_date**: Start date for data retrieval
    - **end_date**: End date for data retrieval
    - **time_scale**: Aggregation time scale (1s, 5s, 10s, 30s, 1m, 5m, 15m, 30m, 1h, 1d)
    """
    try:
        # Validate date range
        if request.start_date > request.end_date:
            raise HTTPException(
                status_code=400, detail="Start date must be before end date"
            )

        # Get ThetaData service instance
        theta_service = get_theta_service()

        # Fetch historical data from ThetaData
        raw_data = theta_service.get_historical_data(
            symbol=request.asset,
            start_date=request.start_date,
            end_date=request.end_date,
            time_scale=request.time_scale.value,
        )

        # Convert raw data to response format
        data_points = []
        for point in raw_data:
            data_points.append(
                HistoricalDataPoint(
                    timestamp=datetime.fromisoformat(point["timestamp"]),
                    open=point["open"],
                    high=point["high"],
                    low=point["low"],
                    close=point["close"],
                    volume=point["volume"],
                )
            )

        return HistoricalDataResponse(
            asset=request.asset,
            time_scale=request.time_scale.value,
            data=data_points,
            total_records=len(data_points),
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error retrieving historical data: {str(e)}"
        )


@router.get("/query", response_model=HistoricalDataResponse)
async def get_historical_data_query(
    asset: str = Query(..., description="Asset symbol"),
    start_date: str = Query(..., description="Start date (YYYY-MM-DD)"),
    end_date: str = Query(..., description="End date (YYYY-MM-DD)"),
    time_scale: TimeScale = Query(..., description="Time scale"),
):
    """
    Alternative GET endpoint for historical data using query parameters.
    """
    from datetime import date

    request = HistoricalDataRequest(
        asset=asset,
        start_date=date.fromisoformat(start_date),
        end_date=date.fromisoformat(end_date),
        time_scale=time_scale,
    )
    return await get_historical_data(request)
