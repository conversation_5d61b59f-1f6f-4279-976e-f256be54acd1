"""
Options chain endpoints
"""

from datetime import date

from fastapi import APIRouter, HTTPException

from app.models.requests import OptionsChainRequest
from app.models.responses import OptionsChainResponse, OptionsData
from app.services.theta_service import get_theta_service

router = APIRouter()


@router.post("/", response_model=OptionsChainResponse)
async def get_options_chain(request: OptionsChainRequest):
    """
    Retrieve historical options chain data for a given asset at a specific time and strike prices.

    - **asset**: Underlying asset symbol
    - **datetime**: Specific datetime for options chain snapshot
    - **strikes**: List of strike prices to retrieve
    - **expiration_date**: Optional filter for specific expiration date
    """
    try:
        # Validate strikes list
        if not request.strikes:
            raise HTTPException(
                status_code=400, detail="At least one strike price must be provided"
            )

        # Get ThetaData service instance
        theta_service = get_theta_service()

        # Fetch options chain data from ThetaData
        raw_options = theta_service.get_options_chain(
            symbol=request.asset,
            target_datetime=request.target_datetime,
            strikes=request.strikes,
            expiration_date=request.expiration_date,
        )

        # Convert raw data to response format
        options_data = []
        for option in raw_options:
            options_data.append(
                OptionsData(
                    strike=option["strike"],
                    expiration=date.fromisoformat(option["expiration"]),
                    call_bid=option.get("call_bid"),
                    call_ask=option.get("call_ask"),
                    call_last=option.get("call_last"),
                    call_volume=option.get("call_volume"),
                    call_open_interest=option.get("call_open_interest"),
                    put_bid=option.get("put_bid"),
                    put_ask=option.get("put_ask"),
                    put_last=option.get("put_last"),
                    put_volume=option.get("put_volume"),
                    put_open_interest=option.get("put_open_interest"),
                )
            )

        return OptionsChainResponse(
            asset=request.asset,
            target_datetime=request.target_datetime,
            options=options_data,
            total_records=len(options_data),
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error retrieving options chain: {str(e)}"
        )
