"""
ThetaData Terminal control endpoints
"""

from fastapi import APIRouter, HTTPException

from app.models.responses import MessageResponse, ThetaStatusResponse
from app.services.theta_service import get_theta_service

router = APIRouter()


@router.post("/start", response_model=MessageResponse)
async def start_theta_terminal():
    """Start ThetaData Terminal."""
    theta_service = get_theta_service()

    if theta_service.is_terminal_running():
        return MessageResponse(
            status="already_running", message="ThetaData Terminal is already running"
        )

    success = theta_service.start_terminal()
    if success:
        return MessageResponse(
            status="started", message="ThetaData Terminal started successfully"
        )
    else:
        raise HTTPException(
            status_code=500, detail="Failed to start ThetaData Terminal"
        )


@router.post("/stop", response_model=MessageResponse)
async def stop_theta_terminal():
    """Stop ThetaData Terminal."""
    theta_service = get_theta_service()

    if not theta_service.is_terminal_running():
        return MessageResponse(
            status="already_stopped", message="ThetaData Terminal is not running"
        )

    theta_service.stop_terminal()
    return MessageResponse(
        status="stopped", message="ThetaData Terminal stopped successfully"
    )


@router.get("/status", response_model=ThetaStatusResponse)
async def get_theta_status():
    """Get ThetaData Terminal status."""
    theta_service = get_theta_service()
    return ThetaStatusResponse(
        running=theta_service.is_terminal_running(),
        host=theta_service.host,
        port=theta_service.port,
        base_url=theta_service.base_url,
    )
