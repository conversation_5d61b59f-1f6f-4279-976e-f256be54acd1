"""
Application configuration settings
"""

import os
import subprocess
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyHttpUrl, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""

    # Project info
    PROJECT_NAME: str = "ThetaData Historical API"
    PROJECT_DESCRIPTION: str = (
        "API for retrieving historical market data and options chain data via ThetaData"
    )
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"

    # Server settings
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    RELOAD: bool = True
    LOG_LEVEL: str = "info"

    # CORS settings
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []

    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # ThetaData Terminal settings
    THETA_JAR_PATH: str = "./ThetaTerminal.jar"
    THETA_HOST: str = "localhost"
    THETA_PORT: int = 25510
    THETA_USERNAME: Optional[str] = None
    THETA_PASSWORD: Optional[str] = None

    # Request settings
    REQUEST_TIMEOUT: int = 30
    MAX_RETRIES: int = 3

    # Rate limiting
    RATE_LIMIT_PER_MINUTE: int = 60

    # Optional database URL for caching
    DATABASE_URL: Optional[str] = None

    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"  # Ignore extra environment variables

    def validate_theta_config(self) -> bool:
        """Validate that required ThetaData configuration is present"""
        valid = True

        # Check if .env file exists
        if not os.path.exists('.env'):
            print("❌ .env file not found!")
            print("   Please create .env file from template:")
            print("   cp env_template.txt .env")
            print("   Then edit .env with your ThetaData credentials")
            return False

        # Check JAR file
        if not os.path.exists(self.THETA_JAR_PATH):
            print(f"❌ ThetaTerminal.jar not found at: {self.THETA_JAR_PATH}")
            print("   Please ensure the ThetaTerminal.jar file is in the correct location")
            valid = False

        # Check credentials
        if not self.THETA_USERNAME or not self.THETA_PASSWORD:
            print("❌ ThetaData credentials not set!")
            print("   Please add your credentials to .env file:")
            print("   THETA_USERNAME=your_username")
            print("   THETA_PASSWORD=your_password")
            valid = False
        else:
            print("✅ ThetaData credentials found")

        # Check Java availability
        try:
            subprocess.run(["java", "-version"], capture_output=True, timeout=5)
            print("✅ Java is available")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("❌ Java not found!")
            print("   Please install Java 11+ and ensure it's in your PATH")
            valid = False

        return valid

    def print_config(self):
        """Print current configuration (masking sensitive data)"""
        print("📋 ThetaData FastAPI Configuration:")
        print(f"   🏠 API Host: {self.HOST}:{self.PORT}")
        print(f"   📊 ThetaData Host: {self.THETA_HOST}:{self.THETA_PORT}")
        print(f"   📁 JAR Path: {self.THETA_JAR_PATH}")
        print(f"   👤 Username: {'***' if self.THETA_USERNAME else 'Not set'}")
        print(f"   🔑 Password: {'***' if self.THETA_PASSWORD else 'Not set'}")
        print(f"   ⏱️  Request Timeout: {self.REQUEST_TIMEOUT}s")
        print(f"   🔄 Max Retries: {self.MAX_RETRIES}")
        print(f"   📊 Log Level: {self.LOG_LEVEL}")


settings = Settings()
