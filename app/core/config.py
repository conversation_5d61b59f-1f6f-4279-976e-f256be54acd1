"""
Application configuration settings
"""

import os
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyHttpUrl, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""

    # Project info
    PROJECT_NAME: str = "ThetaData Historical API"
    PROJECT_DESCRIPTION: str = (
        "API for retrieving historical market data and options chain data via ThetaData"
    )
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"

    # Server settings
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    RELOAD: bool = True
    LOG_LEVEL: str = "info"

    # CORS settings
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []

    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # ThetaData Terminal settings
    THETA_JAR_PATH: str = "./ThetaTerminal.jar"
    THETA_HOST: str = "localhost"
    THETA_PORT: int = 25510
    THETA_USERNAME: Optional[str] = None
    THETA_PASSWORD: Optional[str] = None

    # Request settings
    REQUEST_TIMEOUT: int = 30
    MAX_RETRIES: int = 3

    # Rate limiting
    RATE_LIMIT_PER_MINUTE: int = 60

    # Optional database URL for caching
    DATABASE_URL: Optional[str] = None

    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"  # Ignore extra environment variables


settings = Settings()
