"""
FastAPI application event handlers
"""

import logging
from typing import Callable

from fastapi import FastAPI

from app.services.theta_service import get_theta_service

logger = logging.getLogger(__name__)


def create_start_app_handler(app: FastAPI) -> Callable:
    """Create startup event handler."""

    async def start_app() -> None:
        """Start ThetaData Terminal when the FastAPI app starts."""
        logger.info("🚀 FastAPI startup - initializing ThetaData Terminal...")

        try:
            theta_service = get_theta_service()

            # Check if terminal is already running
            if theta_service.is_terminal_running():
                logger.info("✅ ThetaData Terminal is already running")
                return

            # Start the terminal
            logger.info("🔄 Starting ThetaData Terminal...")
            success = theta_service.start_terminal()

            if success:
                logger.info("✅ ThetaData Terminal started successfully!")
            else:
                logger.error("❌ Failed to start ThetaData Terminal")
                logger.error("   The API will run but ThetaData features may not work")

        except Exception as e:
            logger.error(f"❌ Error during ThetaData Terminal startup: {e}")
            logger.error("   The API will continue to run with fallback data")

    return start_app


def create_stop_app_handler(app: FastAPI) -> Callable:
    """Create shutdown event handler."""

    async def stop_app() -> None:
        """Stop ThetaData Terminal when the FastAPI app shuts down."""
        try:
            theta_service = get_theta_service()
            if theta_service.is_terminal_running():
                logger.info("🛑 Stopping ThetaData Terminal...")
                theta_service.stop_terminal()
                logger.info("✅ ThetaData Terminal stopped")
        except Exception as e:
            logger.error(f"Error stopping ThetaData Terminal: {e}")

    return stop_app
