"""
Database handler for ThetaData API

This module provides optional database functionality for caching and data storage.
Currently supports basic database operations with configurable connection string.
"""

import logging
from typing import Optional, Any, Dict, List
from urllib.parse import urlparse

from app.core.config import settings

logger = logging.getLogger(__name__)

# Global database instance for lazy loading
_db_instance: Optional["DatabaseHandler"] = None


def get_db() -> Optional["DatabaseHandler"]:
    """Get or create database instance (lazy loading)"""
    global _db_instance
    if _db_instance is None and settings.DATABASE_URL:
        _db_instance = DatabaseHandler(settings.DATABASE_URL)
    return _db_instance


class DatabaseHandler:
    """Generic database handler for caching and data storage"""
    
    def __init__(self, database_url: str):
        """Initialize database handler with connection URL"""
        self.database_url = database_url
        self.conn = None
        self.cursor = None
        
        # Parse database URL to determine type
        parsed = urlparse(database_url)
        self.db_type = parsed.scheme.lower()
        
        logger.info(f"Database handler initialized for {self.db_type}")
        
    def connect(self):
        """Create database connection based on URL scheme"""
        try:
            if self.db_type == 'sqlite':
                import sqlite3
                self.conn = sqlite3.connect(self.database_url.replace('sqlite:///', ''))
                self.cursor = self.conn.cursor()
                logger.info("Connected to SQLite database")
                
            elif self.db_type in ['postgresql', 'postgres']:
                try:
                    import psycopg2
                    self.conn = psycopg2.connect(self.database_url)
                    self.cursor = self.conn.cursor()
                    logger.info("Connected to PostgreSQL database")
                except ImportError:
                    logger.error("psycopg2 not installed. Install with: pip install psycopg2-binary")
                    raise
                    
            elif self.db_type == 'mysql':
                try:
                    import pymysql
                    # Parse MySQL URL components
                    parsed = urlparse(self.database_url)
                    self.conn = pymysql.connect(
                        host=parsed.hostname,
                        port=parsed.port or 3306,
                        user=parsed.username,
                        password=parsed.password,
                        database=parsed.path.lstrip('/'),
                    )
                    self.cursor = self.conn.cursor()
                    logger.info("Connected to MySQL database")
                except ImportError:
                    logger.error("pymysql not installed. Install with: pip install pymysql")
                    raise
                    
            else:
                logger.warning(f"Unsupported database type: {self.db_type}")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Error connecting to database: {str(e)}")
            raise
    
    def _ensure_connection(self):
        """Ensure database connection is established"""
        if self.conn is None:
            self.connect()
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """Execute a SELECT query and return results"""
        self._ensure_connection()
        
        try:
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
                
            # Get column names
            columns = [desc[0] for desc in self.cursor.description] if self.cursor.description else []
            
            # Fetch all results and convert to list of dictionaries
            rows = self.cursor.fetchall()
            return [dict(zip(columns, row)) for row in rows]
            
        except Exception as e:
            logger.error(f"Error executing query: {str(e)}")
            raise
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """Execute an INSERT/UPDATE/DELETE query and return affected rows"""
        self._ensure_connection()
        
        try:
            if params:
                affected_rows = self.cursor.execute(query, params)
            else:
                affected_rows = self.cursor.execute(query)
                
            self.conn.commit()
            return affected_rows or self.cursor.rowcount
            
        except Exception as e:
            logger.error(f"Error executing update: {str(e)}")
            if self.conn:
                self.conn.rollback()
            raise
    
    def close(self):
        """Close database connection"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()
            logger.info("Database connection closed")
        except Exception as e:
            logger.error(f"Error closing database connection: {str(e)}")
    
    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()


# Backward compatibility functions
def get_db_instance() -> Optional[DatabaseHandler]:
    """Get database instance (backward compatibility)"""
    return get_db()
