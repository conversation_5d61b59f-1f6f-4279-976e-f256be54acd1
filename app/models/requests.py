"""
Request models for API endpoints
"""

from datetime import date, datetime
from typing import List, Optional

from pydantic import BaseModel, Field

from app.models.enums import TimeScale


class HistoricalDataRequest(BaseModel):
    """Request model for historical data endpoint."""

    asset: str = Field(..., description="Stock symbol (e.g., AAPL, SPY, TSLA)")
    start_date: date = Field(..., description="Start date for data retrieval")
    end_date: date = Field(..., description="End date for data retrieval")
    time_scale: TimeScale = Field(..., description="Aggregation time scale")


class OptionsChainRequest(BaseModel):
    """Request model for options chain endpoint."""

    asset: str = Field(..., description="Underlying asset symbol")
    target_datetime: datetime = Field(
        ..., description="Specific datetime for options chain snapshot"
    )
    strikes: List[float] = Field(..., description="List of strike prices to retrieve")
    expiration_date: Optional[date] = Field(
        None, description="Optional filter for specific expiration date"
    )
