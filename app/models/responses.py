"""
Response models for API endpoints
"""

from datetime import date, datetime
from typing import List, Optional

from pydantic import BaseModel, Field


class HistoricalDataPoint(BaseModel):
    """Individual data point in historical data response."""

    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int


class OptionsData(BaseModel):
    """Options data for a specific strike and expiration."""

    strike: float
    expiration: date
    call_bid: Optional[float] = None
    call_ask: Optional[float] = None
    call_last: Optional[float] = None
    call_volume: Optional[int] = None
    call_open_interest: Optional[int] = None
    put_bid: Optional[float] = None
    put_ask: Optional[float] = None
    put_last: Optional[float] = None
    put_volume: Optional[int] = None
    put_open_interest: Optional[int] = None


class HistoricalDataResponse(BaseModel):
    """Response model for historical data endpoint."""

    asset: str
    time_scale: str
    data: List[HistoricalDataPoint]
    total_records: int


class OptionsChainResponse(BaseModel):
    """Response model for options chain endpoint."""

    asset: str
    target_datetime: datetime
    options: List[OptionsData]
    total_records: int


class HealthResponse(BaseModel):
    """Response model for health check endpoint."""

    status: str
    timestamp: datetime
    theta_terminal_running: bool


class ThetaStatusResponse(BaseModel):
    """Response model for ThetaData Terminal status."""

    running: bool
    host: str
    port: int
    base_url: str


class MessageResponse(BaseModel):
    """Generic message response."""

    status: str
    message: str
