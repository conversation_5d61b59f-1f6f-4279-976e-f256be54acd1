"""
ThetaData Service Module

This module handles communication with ThetaData Terminal for retrieving
historical market data and options chain data.
"""

import json
import logging
import os
import subprocess
import time
from datetime import date, datetime
from typing import Any, Dict, List, Optional

import pandas as pd
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from app.core.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ThetaDataService:
    """Service class for interacting with ThetaData Terminal"""

    def __init__(
        self,
        jar_path: str | None = None,
        host: str | None = None,
        port: int | None = None,
        username: str | None = None,
        password: str | None = None,
    ):
        # Use settings values as defaults
        self.jar_path = jar_path or settings.THETA_JAR_PATH
        self.host = host or settings.THETA_HOST
        self.port = port or settings.THETA_PORT
        self.username = username or settings.THETA_USERNAME
        self.password = password or settings.THETA_PASSWORD
        self.base_url = f"http://{self.host}:{self.port}/v2"
        self.terminal_process = None
        self.session_token = None

        # Setup HTTP session with minimal retries (to avoid blocking)
        self.session = requests.Session()
        self.session.timeout = 5  # 5 second timeout

        self._validate_jar_file()

    def _validate_jar_file(self):
        """Validate that ThetaTerminal.jar exists"""
        if not os.path.exists(self.jar_path):
            raise FileNotFoundError(f"ThetaTerminal.jar not found at {self.jar_path}")

    def start_terminal(self) -> bool:
        """
        Start ThetaData Terminal process
        Returns True if successful, False otherwise
        """
        try:
            logger.info("Starting ThetaData Terminal...")
            logger.info(f"JAR Path: {self.jar_path}")
            logger.info(f"Username: {'***' if self.username else 'NOT SET'}")
            logger.info(f"Password: {'***' if self.password else 'NOT SET'}")
            logger.info(f"Port: {self.port}")

            # Check if JAR file exists
            if not os.path.exists(self.jar_path):
                logger.error(f"ThetaTerminal.jar not found at: {self.jar_path}")
                return False

            # Check if Java is available
            try:
                java_result = subprocess.run(
                    ["java", "-version"], capture_output=True, text=True, timeout=5
                )
                logger.info("✅ Java is available")
            except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                logger.error("❌ Java is not available or not in PATH")
                logger.error("   Please install Java 11+ and ensure it's in your PATH")
                return False

            # Build command args (match the format that worked manually)
            cmd_args = ["java", "-jar", self.jar_path]

            # Add authentication and port if available
            if self.username and self.password:
                # ThetaData Terminal accepts credentials as positional arguments: [username password port]
                cmd_args.extend([self.username, self.password, str(self.port)])
                logger.info(f"🔐 Using credentials with port {self.port}")
            else:
                logger.warning("⚠️  No credentials provided - will try free access mode")
                # For free access, still add port if not default
                if self.port != 25510:
                    cmd_args.append(str(self.port))

            # Build command display (hide password for security)
            cmd_display = f"java -jar {os.path.basename(self.jar_path)}"
            if self.username and self.password:
                cmd_display += f" {self.username} [PASSWORD_HIDDEN] {self.port}"
            elif self.port != 25510:
                cmd_display += f" {self.port}"

            logger.info(f"🚀 Command: {cmd_display}")
            logger.info(
                "   (Real password is used in actual execution, hidden here for security)"
            )

            # Start the process
            self.terminal_process = subprocess.Popen(
                cmd_args,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=os.path.dirname(os.path.abspath(self.jar_path)) or ".",
            )

            # Wait a moment for startup
            logger.info("⏳ Waiting for ThetaData Terminal to start...")
            time.sleep(5)

            # Check if process is still running (simple check)
            if self.terminal_process.poll() is None:
                logger.info("✅ ThetaData Terminal process started successfully")
                return True
            else:
                stdout, stderr = self.terminal_process.communicate()
                logger.error("❌ ThetaData Terminal process exited unexpectedly")
                logger.error(f"Exit code: {self.terminal_process.returncode}")
                if stdout:
                    logger.error(f"STDOUT: {stdout[-500:]}")
                if stderr:
                    logger.error(f"STDERR: {stderr[-500:]}")
                return False

        except Exception as e:
            logger.error(f"❌ Exception while starting ThetaData Terminal: {e}")
            logger.error(
                "   Check that ThetaTerminal.jar is valid and your credentials are correct"
            )
            return False

    def stop_terminal(self):
        """Stop ThetaData Terminal process"""
        if self.terminal_process:
            self.terminal_process.terminate()
            self.terminal_process.wait(timeout=10)
            self.terminal_process = None
            logger.info("ThetaData Terminal stopped")

    def _check_health(self) -> bool:
        """Check if ThetaData Terminal is healthy and responsive"""
        try:
            # Simple check - just verify the process is running
            # ThetaData Terminal might not have an HTTP API or might use different endpoints
            if self.terminal_process and self.terminal_process.poll() is None:
                logger.debug(f"✅ ThetaData Terminal process is running")
                return True
            else:
                logger.debug(f"❌ ThetaData Terminal process not found")
                return False
        except Exception as e:
            logger.debug(f"❌ Health check error: {e}")
            return False

    def _make_request(self, endpoint: str, params: dict = None) -> dict:
        """
        Make HTTP request to ThetaData Terminal

        Args:
            endpoint: API endpoint
            params: Query parameters

        Returns:
            Response data as dictionary
        """
        try:
            url = f"{self.base_url}{endpoint}"
            headers = {}

            if self.session_token:
                headers["X-Theta-Session"] = self.session_token
            if self.username:
                headers["X-Theta-Username"] = self.username

            response = self.session.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()

            return response.json()

        except requests.RequestException as e:
            logger.error(f"Request to {endpoint} failed: {e}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            raise

    def get_historical_data(
        self, symbol: str, start_date: date, end_date: date, time_scale: str
    ) -> List[Dict[str, Any]]:
        """
        Retrieve historical data from ThetaData Terminal

        Args:
            symbol: Asset symbol
            start_date: Start date for data
            end_date: End date for data
            time_scale: Time scale (e.g., "1m", "5m", "1h", "1d")

        Returns:
            List of historical OHLCV data points
        """
        try:
            logger.info(
                f"Fetching historical data for {symbol} from {start_date} to {end_date} with time scale {time_scale}"
            )

            # Format dates for ThetaData API (YYYYMMDD)
            start_date_str = start_date.strftime("%Y%m%d")
            end_date_str = end_date.strftime("%Y%m%d")

            # Convert time scale to milliseconds interval
            interval = self._convert_time_scale(time_scale)

            # Build request parameters for ThetaData Terminal v2 API
            params = {
                "root": symbol.upper(),
                "start_date": start_date_str,
                "end_date": end_date_str,
                "ivl": interval,  # interval in milliseconds for intraday data
            }

            # Determine if symbol is an index or stock and use appropriate endpoint
            endpoint = self._get_historical_endpoint(symbol)
            logger.info(f"Using endpoint {endpoint} for symbol {symbol}")

            response_data = self._make_request(endpoint, params)

            # Transform response to expected format
            historical_data = []
            if "response" in response_data and response_data["response"]:
                # ThetaData v2 API returns data in a specific format
                format_fields = response_data.get("header", {}).get("format", [])

                for data_point in response_data["response"]:
                    if isinstance(data_point, list) and len(data_point) >= 6:
                        # Map the response array to OHLCV data based on format
                        historical_data.append(
                            {
                                "timestamp": (
                                    datetime.fromtimestamp(
                                        data_point[0] / 1000
                                    ).isoformat()
                                    if data_point[0]
                                    else datetime.combine(
                                        start_date, datetime.min.time()
                                    ).isoformat()
                                ),
                                "open": (
                                    float(data_point[1])
                                    if len(data_point) > 1 and data_point[1]
                                    else None
                                ),
                                "high": (
                                    float(data_point[2])
                                    if len(data_point) > 2 and data_point[2]
                                    else None
                                ),
                                "low": (
                                    float(data_point[3])
                                    if len(data_point) > 3 and data_point[3]
                                    else None
                                ),
                                "close": (
                                    float(data_point[4])
                                    if len(data_point) > 4 and data_point[4]
                                    else None
                                ),
                                "volume": (
                                    int(data_point[5])
                                    if len(data_point) > 5 and data_point[5]
                                    else None
                                ),
                            }
                        )

            logger.info(
                f"Retrieved {len(historical_data)} historical data points for {symbol}"
            )
            return historical_data

        except Exception as e:
            logger.error(f"Error fetching historical data: {e}")
            raise Exception(
                f"Failed to retrieve historical data for {symbol}: {str(e)}"
            )

    def _get_historical_endpoint(self, symbol: str) -> str:
        """
        Determine the correct ThetaData v2 API endpoint based on symbol type

        Args:
            symbol: Asset symbol

        Returns:
            Appropriate endpoint path for the symbol type
        """
        # Common index symbols that use the index endpoint
        index_symbols = {
            "SPX",  # S&P 500 Index
            "NDX",  # Nasdaq 100 Index
            "RUT",  # Russell 2000 Index
            "DJX",  # Dow Jones Index
            "VIX",  # Volatility Index
            "SPXW",  # Weekly S&P 500 options
            "XSP",  # Mini S&P 500 Index
            "OEX",  # S&P 100 Index
        }

        symbol_upper = symbol.upper()

        if symbol_upper in index_symbols:
            return "/hist/index/ohlc"
        else:
            # Default to stock endpoint for all other symbols
            # This includes ETFs like SPY, QQQ, IWM which trade as stocks
            return "/hist/stock/ohlc"

    def get_options_chain(
        self,
        symbol: str,
        target_datetime: datetime,
        strikes: List[float],
        expiration_date: Optional[date] = None,
    ) -> List[Dict[str, Any]]:
        """
        Retrieve options chain data from ThetaData

        Args:
            symbol: Underlying asset symbol
            target_datetime: Specific datetime for options chain
            strikes: List of strike prices
            expiration_date: Optional expiration date filter

        Returns:
            List of options data
        """
        try:
            logger.info(
                f"Fetching options chain for {symbol} at {target_datetime} for strikes: {strikes}"
            )

            # Format parameters for ThetaData Terminal v2 API
            start_date_str = target_datetime.strftime("%Y%m%d")
            end_date_str = target_datetime.strftime("%Y%m%d")
            exp_date_str = expiration_date.strftime("%Y%m%d") if expiration_date else ""

            options_data = []

            # Fetch options data for each strike (calls and puts)
            for strike in strikes:
                # Convert strike to ThetaData format (strike in 1/10th cent, so $100 = 100000)
                strike_formatted = int(strike * 1000)

                # Fetch call data
                try:
                    call_params = {
                        "root": symbol.upper(),
                        "exp": exp_date_str,
                        "strike": strike_formatted,
                        "right": "C",  # Call
                        "start_date": start_date_str,
                        "end_date": end_date_str,
                        "ivl": int(target_datetime.timestamp() * 1000)
                        % (24 * 60 * 60 * 1000),  # milliseconds since midnight
                    }

                    # FIXED: Use correct ThetaData Terminal v2 API endpoint for options
                    call_response = self._make_request(
                        "/at_time/option/quote", call_params
                    )
                    call_data = self._parse_option_response(call_response)
                except Exception as e:
                    logger.warning(
                        f"Failed to fetch call data for strike {strike}: {e}"
                    )
                    call_data = {}

                # Fetch put data
                try:
                    put_params = {
                        "root": symbol.upper(),
                        "exp": exp_date_str,
                        "strike": strike_formatted,
                        "right": "P",  # Put
                        "start_date": start_date_str,
                        "end_date": end_date_str,
                        "ivl": int(target_datetime.timestamp() * 1000)
                        % (24 * 60 * 60 * 1000),  # milliseconds since midnight
                    }

                    put_response = self._make_request(
                        "/at_time/option/quote", put_params
                    )
                    put_data = self._parse_option_response(put_response)
                except Exception as e:
                    logger.warning(f"Failed to fetch put data for strike {strike}: {e}")
                    put_data = {}

                # Combine call and put data
                options_data.append(
                    {
                        "strike": strike,
                        "expiration": (
                            expiration_date.isoformat()
                            if expiration_date
                            else target_datetime.date().isoformat()
                        ),
                        "call_bid": call_data.get("bid"),
                        "call_ask": call_data.get("ask"),
                        "call_last": call_data.get("last"),
                        "call_volume": call_data.get("volume"),
                        "call_open_interest": call_data.get("open_interest"),
                        "put_bid": put_data.get("bid"),
                        "put_ask": put_data.get("ask"),
                        "put_last": put_data.get("last"),
                        "put_volume": put_data.get("volume"),
                        "put_open_interest": put_data.get("open_interest"),
                    }
                )

            logger.info(
                f"Retrieved options data for {len(options_data)} strikes for {symbol}"
            )
            return options_data

        except Exception as e:
            logger.error(f"Error fetching options chain: {e}")
            raise Exception(f"Failed to retrieve options chain for {symbol}: {str(e)}")

    def _parse_option_response(self, response_data: dict) -> dict:
        """Parse ThetaData v2 option response into standardized format"""
        try:
            if "response" in response_data and response_data["response"]:
                # ThetaData v2 API returns option data in array format
                # Format: ["ms_of_day","bid_size","bid_exchange","bid","bid_condition","ask_size","ask_exchange","ask","ask_condition","date"]
                data_point = (
                    response_data["response"][0]
                    if isinstance(response_data["response"], list)
                    and response_data["response"]
                    else []
                )

                if isinstance(data_point, list) and len(data_point) >= 8:
                    return {
                        "bid": float(data_point[3]) if data_point[3] else None,
                        "ask": float(data_point[7]) if data_point[7] else None,
                        "last": (
                            float(data_point[3]) if data_point[3] else None
                        ),  # Use bid as last if available
                        "volume": (
                            int(data_point[1]) if data_point[1] else None
                        ),  # bid_size as volume approximation
                        "open_interest": None,  # Not available in this endpoint
                    }
            return {}
        except Exception as e:
            logger.warning(f"Error parsing option response: {e}")
            return {}

    def _convert_time_scale(self, time_scale: str) -> int:
        """
        Convert time scale to ThetaData v2 interval format (milliseconds)

        Args:
            time_scale: Time scale string (e.g., '1s', '5m', '1d')

        Returns:
            Interval in milliseconds for intraday data, or 0 for daily data
        """
        # Convert to milliseconds for ThetaData v2 API
        scale_mapping = {
            "1s": 1000,
            "5s": 5000,
            "10s": 10000,
            "30s": 30000,
            "1m": 60000,
            "5m": 300000,
            "15m": 900000,
            "30m": 1800000,
            "1h": 3600000,
            "1d": 0,  # 0 means end of day data
        }

        return scale_mapping.get(time_scale, 0)

    def is_terminal_running(self) -> bool:
        """Check if ThetaData Terminal is running"""
        return (
            self.terminal_process is not None
            and self.terminal_process.poll() is None
            and self._check_health()
        )

    def __enter__(self):
        """Context manager entry"""
        self.start_terminal()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.stop_terminal()


# Global service instance
theta_service = ThetaDataService()


def get_theta_service() -> ThetaDataService:
    """Get the global theta service instance"""
    return theta_service
