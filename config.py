"""
Configuration module for ThetaData FastAPI application
"""

import os
from typing import Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class ThetaConfig:
    """Configuration class for ThetaData integration"""
    
    # ThetaData Terminal settings
    THETA_JAR_PATH: str = os.getenv('THETA_JAR_PATH', './ThetaTerminal.jar')
    THETA_HOST: str = os.getenv('THETA_HOST', 'localhost')
    THETA_PORT: int = int(os.getenv('THETA_PORT', '25510'))
    THETA_USERNAME: Optional[str] = os.getenv('THETA_USERNAME')
    THETA_PASSWORD: Optional[str] = os.getenv('THETA_PASSWORD')
    
    # FastAPI settings
    API_HOST: str = os.getenv('HOST', '0.0.0.0')
    API_PORT: int = int(os.getenv('PORT', '8000'))
    RELOAD: bool = os.getenv('RELOAD', 'true').lower() == 'true'
    LOG_LEVEL: str = os.getenv('LOG_LEVEL', 'info')
    
    # Request settings
    REQUEST_TIMEOUT: int = int(os.getenv('REQUEST_TIMEOUT', '30'))
    MAX_RETRIES: int = int(os.getenv('MAX_RETRIES', '3'))
    
    # Rate limiting
    RATE_LIMIT_PER_MINUTE: int = int(os.getenv('RATE_LIMIT_PER_MINUTE', '60'))
    
    # Optional database URL for caching
    DATABASE_URL: Optional[str] = os.getenv('DATABASE_URL')
    
    @classmethod
    def validate_theta_config(cls) -> bool:
        """Validate that required ThetaData configuration is present"""
        valid = True
        
        # Check if .env file exists
        if not os.path.exists('.env'):
            print("❌ .env file not found!")
            print("   Please create .env file from template:")
            print("   cp env_template.txt .env")
            print("   Then edit .env with your ThetaData credentials")
            return False
        
        # Check JAR file
        if not os.path.exists(cls.THETA_JAR_PATH):
            print(f"❌ ThetaTerminal.jar not found at: {cls.THETA_JAR_PATH}")
            print("   Please ensure the ThetaTerminal.jar file is in the correct location")
            valid = False
            
        # Check credentials
        if not cls.THETA_USERNAME or not cls.THETA_PASSWORD:
            print("❌ ThetaData credentials not set!")
            print("   Please add your credentials to .env file:")
            print("   THETA_USERNAME=your_username")
            print("   THETA_PASSWORD=your_password")
            valid = False
        else:
            print("✅ ThetaData credentials found")
            
        # Check Java availability
        try:
            import subprocess
            subprocess.run(["java", "-version"], capture_output=True, timeout=5)
            print("✅ Java is available")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("❌ Java not found!")
            print("   Please install Java 11+ and ensure it's in your PATH")
            valid = False
            
        return valid
    
    @classmethod
    def print_config(cls):
        """Print current configuration (masking sensitive data)"""
        print("📋 ThetaData FastAPI Configuration:")
        print(f"   🏠 API Host: {cls.API_HOST}:{cls.API_PORT}")
        print(f"   📊 ThetaData Host: {cls.THETA_HOST}:{cls.THETA_PORT}")
        print(f"   📁 JAR Path: {cls.THETA_JAR_PATH}")
        print(f"   👤 Username: {'***' if cls.THETA_USERNAME else 'Not set'}")
        print(f"   🔑 Password: {'***' if cls.THETA_PASSWORD else 'Not set'}")
        print(f"   ⏱️  Request Timeout: {cls.REQUEST_TIMEOUT}s")
        print(f"   🔄 Max Retries: {cls.MAX_RETRIES}")
        print(f"   📊 Log Level: {cls.LOG_LEVEL}")

# Global config instance
config = ThetaConfig() 