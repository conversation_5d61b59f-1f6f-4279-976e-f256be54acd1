<?xml version="1.0" ?>
<coverage version="7.10.1" timestamp="1754163510665" lines-valid="433" lines-covered="222" line-rate="0.5127" branches-covered="0" branches-valid="0" branch-rate="0" complexity="0">
	<!-- Generated by coverage.py: https://coverage.readthedocs.io/en/7.10.1 -->
	<!-- Based on https://raw.githubusercontent.com/cobertura/web/master/htdocs/xml/coverage-04.dtd -->
	<sources>
		<source>/Users/<USER>/Documents/code/api/app</source>
	</sources>
	<packages>
		<package name="." line-rate="0.9333" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
					</lines>
				</class>
				<class name="main.py" filename="main.py" complexity="0" line-rate="0.9286" branch-rate="0">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="13" hits="1"/>
						<line number="16" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="0"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="40" hits="1"/>
						<line number="42" hits="1"/>
						<line number="45" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api" line-rate="1" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
			</classes>
		</package>
		<package name="api.api_v1" line-rate="1" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/api_v1/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="api.py" filename="api/api_v1/api.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.api_v1.endpoints" line-rate="0.5125" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/api_v1/endpoints/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="health.py" filename="api/api_v1/endpoints/health.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
					</lines>
				</class>
				<class name="historical_data.py" filename="api/api_v1/endpoints/historical_data.py" complexity="0" line-rate="0.44" branch-rate="0">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="26" hits="0"/>
						<line number="28" hits="0"/>
						<line number="29" hits="0"/>
						<line number="35" hits="0"/>
						<line number="38" hits="0"/>
						<line number="46" hits="0"/>
						<line number="47" hits="0"/>
						<line number="48" hits="0"/>
						<line number="59" hits="0"/>
						<line number="66" hits="0"/>
						<line number="67" hits="0"/>
						<line number="73" hits="1"/>
						<line number="74" hits="1"/>
						<line number="83" hits="0"/>
						<line number="85" hits="0"/>
						<line number="91" hits="0"/>
					</lines>
				</class>
				<class name="options_chain.py" filename="api/api_v1/endpoints/options_chain.py" complexity="0" line-rate="0.4211" branch-rate="0">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="25" hits="0"/>
						<line number="27" hits="0"/>
						<line number="28" hits="0"/>
						<line number="34" hits="0"/>
						<line number="37" hits="0"/>
						<line number="45" hits="0"/>
						<line number="46" hits="0"/>
						<line number="47" hits="0"/>
						<line number="64" hits="0"/>
						<line number="71" hits="0"/>
						<line number="72" hits="0"/>
					</lines>
				</class>
				<class name="theta_control.py" filename="api/api_v1/endpoints/theta_control.py" complexity="0" line-rate="0.4167" branch-rate="0">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="10" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
						<line number="26" hits="0"/>
						<line number="31" hits="0"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="40" hits="0"/>
						<line number="42" hits="0"/>
						<line number="43" hits="0"/>
						<line number="48" hits="0"/>
						<line number="49" hits="0"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="core" line-rate="0.6377" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="core/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="config.py" filename="core/config.py" complexity="0" line-rate="0.9429" branch-rate="0">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="11" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="0"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="0"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="49" hits="1"/>
						<line number="52" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="60" hits="1"/>
					</lines>
				</class>
				<class name="events.py" filename="core/events.py" complexity="0" line-rate="0.3235" branch-rate="0">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="20" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
						<line number="26" hits="0"/>
						<line number="27" hits="0"/>
						<line number="28" hits="0"/>
						<line number="31" hits="0"/>
						<line number="32" hits="0"/>
						<line number="34" hits="0"/>
						<line number="35" hits="0"/>
						<line number="37" hits="0"/>
						<line number="38" hits="0"/>
						<line number="40" hits="0"/>
						<line number="41" hits="0"/>
						<line number="42" hits="0"/>
						<line number="44" hits="1"/>
						<line number="47" hits="1"/>
						<line number="50" hits="1"/>
						<line number="52" hits="0"/>
						<line number="53" hits="0"/>
						<line number="54" hits="0"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0"/>
						<line number="57" hits="0"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="61" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="models" line-rate="1" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="models/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="enums.py" filename="models/enums.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="8" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
					</lines>
				</class>
				<class name="requests.py" filename="models/requests.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
					</lines>
				</class>
				<class name="responses.py" filename="models/responses.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="38" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="47" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="56" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="64" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="73" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="services" line-rate="0.2356" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="services/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="theta_service.py" filename="services/theta_service.py" complexity="0" line-rate="0.2356" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="25" hits="1"/>
						<line number="28" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="50" hits="1"/>
						<line number="52" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="0"/>
						<line number="59" hits="1"/>
						<line number="64" hits="0"/>
						<line number="65" hits="0"/>
						<line number="66" hits="0"/>
						<line number="67" hits="0"/>
						<line number="68" hits="0"/>
						<line number="69" hits="0"/>
						<line number="72" hits="0"/>
						<line number="73" hits="0"/>
						<line number="74" hits="0"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="86" hits="0"/>
						<line number="89" hits="0"/>
						<line number="91" hits="0"/>
						<line number="92" hits="0"/>
						<line number="94" hits="0"/>
						<line number="96" hits="0"/>
						<line number="97" hits="0"/>
						<line number="100" hits="0"/>
						<line number="101" hits="0"/>
						<line number="102" hits="0"/>
						<line number="103" hits="0"/>
						<line number="104" hits="0"/>
						<line number="106" hits="0"/>
						<line number="107" hits="0"/>
						<line number="110" hits="0"/>
						<line number="119" hits="0"/>
						<line number="120" hits="0"/>
						<line number="123" hits="0"/>
						<line number="124" hits="0"/>
						<line number="125" hits="0"/>
						<line number="127" hits="0"/>
						<line number="128" hits="0"/>
						<line number="129" hits="0"/>
						<line number="130" hits="0"/>
						<line number="131" hits="0"/>
						<line number="132" hits="0"/>
						<line number="133" hits="0"/>
						<line number="134" hits="0"/>
						<line number="136" hits="0"/>
						<line number="137" hits="0"/>
						<line number="138" hits="0"/>
						<line number="139" hits="0"/>
						<line number="141" hits="1"/>
						<line number="143" hits="0"/>
						<line number="144" hits="0"/>
						<line number="145" hits="0"/>
						<line number="146" hits="0"/>
						<line number="147" hits="0"/>
						<line number="149" hits="1"/>
						<line number="151" hits="0"/>
						<line number="154" hits="0"/>
						<line number="155" hits="0"/>
						<line number="156" hits="0"/>
						<line number="158" hits="0"/>
						<line number="159" hits="0"/>
						<line number="160" hits="0"/>
						<line number="161" hits="0"/>
						<line number="162" hits="0"/>
						<line number="166" hits="1"/>
						<line number="177" hits="0"/>
						<line number="178" hits="0"/>
						<line number="179" hits="0"/>
						<line number="181" hits="0"/>
						<line number="182" hits="0"/>
						<line number="183" hits="0"/>
						<line number="184" hits="0"/>
						<line number="186" hits="0"/>
						<line number="187" hits="0"/>
						<line number="189" hits="0"/>
						<line number="191" hits="0"/>
						<line number="192" hits="0"/>
						<line number="193" hits="0"/>
						<line number="194" hits="0"/>
						<line number="195" hits="0"/>
						<line number="196" hits="0"/>
						<line number="198" hits="1"/>
						<line number="217" hits="0"/>
						<line number="218" hits="0"/>
						<line number="221" hits="0"/>
						<line number="222" hits="0"/>
						<line number="225" hits="0"/>
						<line number="228" hits="0"/>
						<line number="236" hits="0"/>
						<line number="237" hits="0"/>
						<line number="239" hits="0"/>
						<line number="242" hits="0"/>
						<line number="243" hits="0"/>
						<line number="245" hits="0"/>
						<line number="247" hits="0"/>
						<line number="248" hits="0"/>
						<line number="250" hits="0"/>
						<line number="259" hits="0"/>
						<line number="260" hits="0"/>
						<line number="262" hits="0"/>
						<line number="263" hits="0"/>
						<line number="264" hits="0"/>
						<line number="266" hits="1"/>
						<line number="277" hits="0"/>
						<line number="288" hits="0"/>
						<line number="290" hits="0"/>
						<line number="291" hits="0"/>
						<line number="295" hits="0"/>
						<line number="297" hits="1"/>
						<line number="316" hits="0"/>
						<line number="317" hits="0"/>
						<line number="320" hits="0"/>
						<line number="321" hits="0"/>
						<line number="322" hits="0"/>
						<line number="324" hits="0"/>
						<line number="327" hits="0"/>
						<line number="329" hits="0"/>
						<line number="332" hits="0"/>
						<line number="333" hits="0"/>
						<line number="344" hits="0"/>
						<line number="345" hits="0"/>
						<line number="346" hits="0"/>
						<line number="347" hits="0"/>
						<line number="348" hits="0"/>
						<line number="351" hits="0"/>
						<line number="352" hits="0"/>
						<line number="362" hits="0"/>
						<line number="363" hits="0"/>
						<line number="364" hits="0"/>
						<line number="365" hits="0"/>
						<line number="366" hits="0"/>
						<line number="369" hits="0"/>
						<line number="384" hits="0"/>
						<line number="385" hits="0"/>
						<line number="387" hits="0"/>
						<line number="388" hits="0"/>
						<line number="389" hits="0"/>
						<line number="391" hits="1"/>
						<line number="393" hits="0"/>
						<line number="394" hits="0"/>
						<line number="397" hits="0"/>
						<line number="399" hits="0"/>
						<line number="400" hits="0"/>
						<line number="407" hits="0"/>
						<line number="408" hits="0"/>
						<line number="409" hits="0"/>
						<line number="410" hits="0"/>
						<line number="412" hits="1"/>
						<line number="423" hits="0"/>
						<line number="436" hits="0"/>
						<line number="438" hits="1"/>
						<line number="440" hits="1"/>
						<line number="446" hits="1"/>
						<line number="448" hits="0"/>
						<line number="449" hits="0"/>
						<line number="451" hits="1"/>
						<line number="453" hits="0"/>
						<line number="456" hits="1"/>
						<line number="458" hits="1"/>
						<line number="460" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
	</packages>
</coverage>
