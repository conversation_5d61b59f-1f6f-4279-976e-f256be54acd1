version: '3.8'

services:
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - RELOAD=false
      - LOG_LEVEL=info
    env_file:
      - .env
    volumes:
      - ./ThetaTerminal.jar:/app/ThetaTerminal.jar:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a database service if needed
  # postgres:
  #   image: postgres:15
  #   environment:
  #     POSTGRES_DB: thetadata
  #     POSTGRES_USER: postgres
  #     POSTGRES_PASSWORD: password
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   ports:
  #     - "5432:5432"

# volumes:
#   postgres_data:
