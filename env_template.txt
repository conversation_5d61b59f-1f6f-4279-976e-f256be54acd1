# Copy this file to .env and customize your settings

# FastAPI Configuration
HOST=0.0.0.0
PORT=8000
RELOAD=true
LOG_LEVEL=info

# ThetaData Terminal Configuration
THETA_JAR_PATH=./ThetaTerminal.jar
THETA_HOST=localhost
THETA_PORT=25510
THETA_USERNAME=your_username_here
THETA_PASSWORD=your_password_here

# Request Configuration
REQUEST_TIMEOUT=30
MAX_RETRIES=3

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# Optional: Database Configuration (for caching, etc.)
# DATABASE_URL=sqlite:///./thetadata.db

# Optional: Production Settings
# RELOAD=false
# LOG_LEVEL=warning 