<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_6b508a39.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">51%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-02 15:38 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html">app/__init__.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c___init___py.html">app/api/__init__.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a18d7105cd8fee1___init___py.html">app/api/api_v1/__init__.py</a></td>
                <td class="name left"><a href="z_8a18d7105cd8fee1___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a18d7105cd8fee1_api_py.html">app/api/api_v1/api.py</a></td>
                <td class="name left"><a href="z_8a18d7105cd8fee1_api_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a53996b8c220380e___init___py.html">app/api/api_v1/endpoints/__init__.py</a></td>
                <td class="name left"><a href="z_a53996b8c220380e___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a53996b8c220380e_health_py.html#t15">app/api/api_v1/endpoints/health.py</a></td>
                <td class="name left"><a href="z_a53996b8c220380e_health_py.html#t15"><data value='root'>root</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a53996b8c220380e_health_py.html#t24">app/api/api_v1/endpoints/health.py</a></td>
                <td class="name left"><a href="z_a53996b8c220380e_health_py.html#t24"><data value='health_check'>health_check</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a53996b8c220380e_health_py.html">app/api/api_v1/endpoints/health.py</a></td>
                <td class="name left"><a href="z_a53996b8c220380e_health_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a53996b8c220380e_historical_data_py.html#t17">app/api/api_v1/endpoints/historical_data.py</a></td>
                <td class="name left"><a href="z_a53996b8c220380e_historical_data_py.html#t17"><data value='get_historical_data'>get_historical_data</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a53996b8c220380e_historical_data_py.html#t74">app/api/api_v1/endpoints/historical_data.py</a></td>
                <td class="name left"><a href="z_a53996b8c220380e_historical_data_py.html#t74"><data value='get_historical_data_query'>get_historical_data_query</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a53996b8c220380e_historical_data_py.html">app/api/api_v1/endpoints/historical_data.py</a></td>
                <td class="name left"><a href="z_a53996b8c220380e_historical_data_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a53996b8c220380e_options_chain_py.html#t16">app/api/api_v1/endpoints/options_chain.py</a></td>
                <td class="name left"><a href="z_a53996b8c220380e_options_chain_py.html#t16"><data value='get_options_chain'>get_options_chain</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a53996b8c220380e_options_chain_py.html">app/api/api_v1/endpoints/options_chain.py</a></td>
                <td class="name left"><a href="z_a53996b8c220380e_options_chain_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a53996b8c220380e_theta_control_py.html#t14">app/api/api_v1/endpoints/theta_control.py</a></td>
                <td class="name left"><a href="z_a53996b8c220380e_theta_control_py.html#t14"><data value='start_theta_terminal'>start_theta_terminal</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a53996b8c220380e_theta_control_py.html#t38">app/api/api_v1/endpoints/theta_control.py</a></td>
                <td class="name left"><a href="z_a53996b8c220380e_theta_control_py.html#t38"><data value='stop_theta_terminal'>stop_theta_terminal</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a53996b8c220380e_theta_control_py.html#t56">app/api/api_v1/endpoints/theta_control.py</a></td>
                <td class="name left"><a href="z_a53996b8c220380e_theta_control_py.html#t56"><data value='get_theta_status'>get_theta_status</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a53996b8c220380e_theta_control_py.html">app/api/api_v1/endpoints/theta_control.py</a></td>
                <td class="name left"><a href="z_a53996b8c220380e_theta_control_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417___init___py.html">app/core/__init__.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t30">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t30"><data value='assemble_cors_origins'>Settings.assemble_cors_origins</data></a></td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="3 5">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_events_py.html#t15">app/core/events.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_events_py.html#t15"><data value='create_start_app_handler'>create_start_app_handler</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_events_py.html#t18">app/core/events.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_events_py.html#t18"><data value='start_app'>create_start_app_handler.start_app</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_events_py.html#t47">app/core/events.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_events_py.html#t47"><data value='create_stop_app_handler'>create_stop_app_handler</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_events_py.html#t50">app/core/events.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_events_py.html#t50"><data value='stop_app'>create_stop_app_handler.stop_app</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_events_py.html">app/core/events.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_events_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t13">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t13"><data value='get_application'>get_application</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b___init___py.html">app/models/__init__.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_enums_py.html">app/models/enums.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_enums_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_requests_py.html">app/models/requests.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_requests_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_responses_py.html">app/models/responses.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_responses_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>45</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="45 45">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html">app/services/__init__.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t28">app/services/theta_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t28"><data value='init__'>ThetaDataService.__init__</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t52">app/services/theta_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t52"><data value='validate_jar_file'>ThetaDataService._validate_jar_file</data></a></td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="1 2">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t59">app/services/theta_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t59"><data value='start_terminal'>ThetaDataService.start_terminal</data></a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t141">app/services/theta_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t141"><data value='stop_terminal'>ThetaDataService.stop_terminal</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t149">app/services/theta_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t149"><data value='check_health'>ThetaDataService._check_health</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t166">app/services/theta_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t166"><data value='make_request'>ThetaDataService._make_request</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t198">app/services/theta_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t198"><data value='get_historical_data'>ThetaDataService.get_historical_data</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t266">app/services/theta_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t266"><data value='get_historical_endpoint'>ThetaDataService._get_historical_endpoint</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t297">app/services/theta_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t297"><data value='get_options_chain'>ThetaDataService.get_options_chain</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t391">app/services/theta_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t391"><data value='parse_option_response'>ThetaDataService._parse_option_response</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t412">app/services/theta_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t412"><data value='convert_time_scale'>ThetaDataService._convert_time_scale</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t438">app/services/theta_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t438"><data value='is_terminal_running'>ThetaDataService.is_terminal_running</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t446">app/services/theta_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t446"><data value='enter__'>ThetaDataService.__enter__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t451">app/services/theta_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t451"><data value='exit__'>ThetaDataService.__exit__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t458">app/services/theta_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html#t458"><data value='get_theta_service'>get_theta_service</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html">app/services/theta_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_theta_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>433</td>
                <td>211</td>
                <td>0</td>
                <td class="right" data-ratio="222 433">51%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-02 15:38 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
