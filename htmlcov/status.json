{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.1", "globals": "fe7824843ec1efde84d8c32f1a918d8f", "files": {"z_5f5a17c013354698___init___py": {"hash": "214a9aa603a3b507b08aa5e0d28d4616", "index": {"url": "z_5f5a17c013354698___init___py.html", "file": "app/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cfb6adc3f81c8e3c___init___py": {"hash": "fefdec8654b9085ff4c8c3aefcd40aa8", "index": {"url": "z_cfb6adc3f81c8e3c___init___py.html", "file": "app/api/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8a18d7105cd8fee1___init___py": {"hash": "87cdb8377982d88cbe7ac822be509c30", "index": {"url": "z_8a18d7105cd8fee1___init___py.html", "file": "app/api/api_v1/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8a18d7105cd8fee1_api_py": {"hash": "dcd2c952e455c6326d6df0e1bd498572", "index": {"url": "z_8a18d7105cd8fee1_api_py.html", "file": "app/api/api_v1/api.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a53996b8c220380e___init___py": {"hash": "82c1b2dfa90aef929417c827d5d2f82c", "index": {"url": "z_a53996b8c220380e___init___py.html", "file": "app/api/api_v1/endpoints/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a53996b8c220380e_health_py": {"hash": "6470ce84a1dc0558701a07570adf0393", "index": {"url": "z_a53996b8c220380e_health_py.html", "file": "app/api/api_v1/endpoints/health.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a53996b8c220380e_historical_data_py": {"hash": "ba8a2b178a03d66ed99fd08df66d6560", "index": {"url": "z_a53996b8c220380e_historical_data_py.html", "file": "app/api/api_v1/endpoints/historical_data.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a53996b8c220380e_options_chain_py": {"hash": "36b52d6ea2e143cc6ea1c6a63ca9703b", "index": {"url": "z_a53996b8c220380e_options_chain_py.html", "file": "app/api/api_v1/endpoints/options_chain.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 19, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a53996b8c220380e_theta_control_py": {"hash": "8890fe12c78c99305e78f566205b6c05", "index": {"url": "z_a53996b8c220380e_theta_control_py.html", "file": "app/api/api_v1/endpoints/theta_control.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417___init___py": {"hash": "808eaf4614496943277119cf504796c3", "index": {"url": "z_8f7e1016f2d37417___init___py.html", "file": "app/core/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_config_py": {"hash": "492a15d434a81c8d1c7005da9453121a", "index": {"url": "z_8f7e1016f2d37417_config_py.html", "file": "app/core/config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_events_py": {"hash": "c95a38221ccdf2ffeeb929c35f24a36e", "index": {"url": "z_8f7e1016f2d37417_events_py.html", "file": "app/core/events.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 23, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5a17c013354698_main_py": {"hash": "b1248cea3aabaab8e7d995c19b674003", "index": {"url": "z_5f5a17c013354698_main_py.html", "file": "app/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b___init___py": {"hash": "0c3ee42e1cb81507e49f2775c6a98a03", "index": {"url": "z_6c0e4b930745278b___init___py.html", "file": "app/models/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b_enums_py": {"hash": "28266a76b1f0df2fbd3251fd619e3cc4", "index": {"url": "z_6c0e4b930745278b_enums_py.html", "file": "app/models/enums.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b_requests_py": {"hash": "0cf766a694eca691f3627143951a9edf", "index": {"url": "z_6c0e4b930745278b_requests_py.html", "file": "app/models/requests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b_responses_py": {"hash": "6a7d9eddd8efe85622243c9701e0b1f6", "index": {"url": "z_6c0e4b930745278b_responses_py.html", "file": "app/models/responses.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 45, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69___init___py": {"hash": "a9ea5624e03dea58cca3ea6eb38400c9", "index": {"url": "z_c318f3fa19a49f69___init___py.html", "file": "app/services/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_theta_service_py": {"hash": "7082ac7275390eaf6b250831adc01611", "index": {"url": "z_c318f3fa19a49f69_theta_service_py.html", "file": "app/services/theta_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 191, "n_excluded": 0, "n_missing": 146, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}