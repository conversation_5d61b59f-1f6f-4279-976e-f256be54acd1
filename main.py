from fastapi import FastAP<PERSON>, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Union
from datetime import datetime, date
from enum import Enum
import uvicorn
from theta_service import get_theta_service

app = FastAPI(
    title="ThetaData Historical API",
    description="API for retrieving historical market data and options chain data via ThetaData",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Start ThetaData Terminal when the FastAPI app starts"""
    import logging
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 FastAPI startup - initializing ThetaData Terminal...")
    
    try:
        theta_service = get_theta_service()
        
        # Check if terminal is already running
        if theta_service.is_terminal_running():
            logger.info("✅ ThetaData Terminal is already running")
            return
        
        # Start the terminal
        logger.info("🔄 Starting ThetaData Terminal...")
        success = theta_service.start_terminal()
        
        if success:
            logger.info("✅ ThetaData Terminal started successfully!")
        else:
            logger.error("❌ Failed to start ThetaData Terminal")
            logger.error("   The API will run but ThetaData features may not work")
            
    except Exception as e:
        logger.error(f"❌ Error during ThetaData Terminal startup: {e}")
        logger.error("   The API will continue to run with fallback data")

@app.on_event("shutdown")
async def shutdown_event():
    """Stop ThetaData Terminal when the FastAPI app shuts down"""
    import logging
    logger = logging.getLogger(__name__)
    
    try:
        theta_service = get_theta_service()
        if theta_service.is_terminal_running():
            logger.info("🛑 Stopping ThetaData Terminal...")
            theta_service.stop_terminal()
            logger.info("✅ ThetaData Terminal stopped")
    except Exception as e:
        logger.error(f"Error stopping ThetaData Terminal: {e}")

# Enums for time scales
class TimeScale(str, Enum):
    one_second = "1s"
    five_seconds = "5s"
    ten_seconds = "10s"
    thirty_seconds = "30s"
    one_minute = "1m"
    five_minutes = "5m"
    fifteen_minutes = "15m"
    thirty_minutes = "30m"
    one_hour = "1h"
    one_day = "1d"

# Request/Response Models
class HistoricalDataRequest(BaseModel):
    asset: str
    start_date: date
    end_date: date
    time_scale: TimeScale

class OptionsChainRequest(BaseModel):
    asset: str
    datetime: datetime
    strikes: List[float]
    expiration_date: Optional[date] = None

class HistoricalDataPoint(BaseModel):
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int

class OptionsData(BaseModel):
    strike: float
    expiration: date
    call_bid: Optional[float] = None
    call_ask: Optional[float] = None
    call_last: Optional[float] = None
    call_volume: Optional[int] = None
    call_open_interest: Optional[int] = None
    put_bid: Optional[float] = None
    put_ask: Optional[float] = None
    put_last: Optional[float] = None
    put_volume: Optional[int] = None
    put_open_interest: Optional[int] = None

class HistoricalDataResponse(BaseModel):
    asset: str
    time_scale: str
    data: List[HistoricalDataPoint]
    total_records: int

class OptionsChainResponse(BaseModel):
    asset: str
    datetime: datetime
    options: List[OptionsData]
    total_records: int

@app.get("/")
async def root():
    return {"message": "ThetaData Historical API", "status": "running"}

@app.get("/health")
async def health_check():
    theta_service = get_theta_service()
    return {
        "status": "healthy", 
        "timestamp": datetime.now(),
        "theta_terminal_running": theta_service.is_terminal_running()
    }

@app.post("/theta/start")
async def start_theta_terminal():
    """Start ThetaData Terminal"""
    theta_service = get_theta_service()
    
    if theta_service.is_terminal_running():
        return {"status": "already_running", "message": "ThetaData Terminal is already running"}
    
    success = theta_service.start_terminal()
    if success:
        return {"status": "started", "message": "ThetaData Terminal started successfully"}
    else:
        raise HTTPException(status_code=500, detail="Failed to start ThetaData Terminal")

@app.post("/theta/stop")
async def stop_theta_terminal():
    """Stop ThetaData Terminal"""
    theta_service = get_theta_service()
    
    if not theta_service.is_terminal_running():
        return {"status": "already_stopped", "message": "ThetaData Terminal is not running"}
    
    theta_service.stop_terminal()
    return {"status": "stopped", "message": "ThetaData Terminal stopped successfully"}

@app.get("/theta/status")
async def get_theta_status():
    """Get ThetaData Terminal status"""
    theta_service = get_theta_service()
    return {
        "running": theta_service.is_terminal_running(),
        "host": theta_service.host,
        "port": theta_service.port,
        "base_url": theta_service.base_url
    }

@app.post("/historical-data", response_model=HistoricalDataResponse)
async def get_historical_data(request: HistoricalDataRequest):
    """
    Retrieve historical market data for a given asset within a date range and time scale.
    
    - **asset**: Stock symbol (e.g., AAPL, SPY, TSLA)
    - **start_date**: Start date for data retrieval
    - **end_date**: End date for data retrieval  
    - **time_scale**: Aggregation time scale (1s, 5s, 10s, 30s, 1m, 5m, 15m, 30m, 1h, 1d)
    """
    try:
        # Validate date range
        if request.start_date > request.end_date:
            raise HTTPException(status_code=400, detail="Start date must be before end date")
        
        # Get ThetaData service instance
        theta_service = get_theta_service()
        
        # Fetch historical data from ThetaData
        raw_data = theta_service.get_historical_data(
            symbol=request.asset,
            start_date=request.start_date,
            end_date=request.end_date,
            time_scale=request.time_scale.value
        )
        
        # Convert raw data to response format
        data_points = []
        for point in raw_data:
            data_points.append(
                HistoricalDataPoint(
                    timestamp=datetime.fromisoformat(point["timestamp"]),
                    open=point["open"],
                    high=point["high"],
                    low=point["low"],
                    close=point["close"],
                    volume=point["volume"]
                )
            )
        
        return HistoricalDataResponse(
            asset=request.asset,
            time_scale=request.time_scale.value,
            data=data_points,
            total_records=len(data_points)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving historical data: {str(e)}")

@app.post("/options-chain", response_model=OptionsChainResponse)
async def get_options_chain(request: OptionsChainRequest):
    """
    Retrieve historical options chain data for a given asset at a specific time and strike prices.
    
    - **asset**: Underlying asset symbol
    - **datetime**: Specific datetime for options chain snapshot
    - **strikes**: List of strike prices to retrieve
    - **expiration_date**: Optional filter for specific expiration date
    """
    try:
        # Validate strikes list
        if not request.strikes:
            raise HTTPException(status_code=400, detail="At least one strike price must be provided")
        
        # Get ThetaData service instance
        theta_service = get_theta_service()
        
        # Fetch options chain data from ThetaData
        raw_options = theta_service.get_options_chain(
            symbol=request.asset,
            target_datetime=request.datetime,
            strikes=request.strikes,
            expiration_date=request.expiration_date
        )
        
        # Convert raw data to response format
        options_data = []
        for option in raw_options:
            options_data.append(
                OptionsData(
                    strike=option["strike"],
                    expiration=date.fromisoformat(option["expiration"]),
                    call_bid=option.get("call_bid"),
                    call_ask=option.get("call_ask"),
                    call_last=option.get("call_last"),
                    call_volume=option.get("call_volume"),
                    call_open_interest=option.get("call_open_interest"),
                    put_bid=option.get("put_bid"),
                    put_ask=option.get("put_ask"),
                    put_last=option.get("put_last"),
                    put_volume=option.get("put_volume"),
                    put_open_interest=option.get("put_open_interest")
                )
            )
        
        return OptionsChainResponse(
            asset=request.asset,
            datetime=request.datetime,
            options=options_data,
            total_records=len(options_data)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving options chain: {str(e)}")

@app.get("/historical-data-query")
async def get_historical_data_query(
    asset: str = Query(..., description="Asset symbol"),
    start_date: date = Query(..., description="Start date (YYYY-MM-DD)"),
    end_date: date = Query(..., description="End date (YYYY-MM-DD)"),
    time_scale: TimeScale = Query(..., description="Time scale")
):
    """
    Alternative GET endpoint for historical data using query parameters.
    """
    request = HistoricalDataRequest(
        asset=asset,
        start_date=start_date,
        end_date=end_date,
        time_scale=time_scale
    )
    return await get_historical_data(request)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000) 