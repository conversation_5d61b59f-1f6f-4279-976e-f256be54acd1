#!/usr/bin/env python3
"""
Main entry point for the ThetaData API application
"""

import sys
import uvicorn

from app.core.config import settings


def main():
    """Main entry point with configuration validation and startup"""
    print("🚀 Starting ThetaData FastAPI Server")
    print("=" * 50)
    
    # Print configuration
    settings.print_config()
    print()
    
    # Validate ThetaData configuration
    if not settings.validate_theta_config():
        print("❌ Configuration validation failed!")
        print("Please check your configuration and try again.")
        sys.exit(1)
    
    print("✅ Configuration validated successfully!")
    print()
    
    print(f"📖 API Documentation: http://{settings.HOST}:{settings.PORT}/docs")
    print(f"🔍 Interactive API: http://{settings.HOST}:{settings.PORT}/redoc")
    print(f"❤️  Health Check: http://{settings.HOST}:{settings.PORT}/health")
    print()
    
    print("Starting server...")
    
    # Configure reload settings
    reload_dirs = ["."] if settings.RELOAD else None
    reload_includes = ["*.py"] if settings.RELOAD else None
    reload_excludes = [
        ".venv/**",
        "**/.venv/**",
        "__pycache__/**",
        "**/__pycache__/**",
        ".git/**",
        "htmlcov/**",
        "*.pyc"
    ] if settings.RELOAD else None

    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.RELOAD,
        log_level=settings.LOG_LEVEL.lower(),
        reload_dirs=reload_dirs,
        reload_includes=reload_includes,
        reload_excludes=reload_excludes,
    )


if __name__ == "__main__":
    main()
