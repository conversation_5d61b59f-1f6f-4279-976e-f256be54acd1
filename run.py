
#!/usr/bin/env python3
"""
Startup script for ThetaData FastAPI application
"""

import uvicorn
import sys
from config import config

if __name__ == "__main__":
    print("🚀 Starting FastAPI Server")
    print("=" * 50)
    
    # Print configuration
    config.print_config()
    print()
    
    # Validate ThetaData configuration
    if not config.validate_theta_config():
        print("❌ Configuration validation failed!")
        print("Please check your configuration and try again.")
        sys.exit(1)
    
    print("✅ Configuration validated successfully!")
    print()
    
    print(f"📖 API Documentation: http://{config.API_HOST}:{config.API_PORT}/docs")
    print(f"🔍 Interactive API: http://{config.API_HOST}:{config.API_PORT}/redoc")
    print(f"❤️  Health Check: http://{config.API_HOST}:{config.API_PORT}/health")
    print()
    
    print("Starting server...")
    
    # Only watch specific files/directories, excluding .venv
    reload_dirs = ["."] if config.RELOAD else None
    reload_includes = ["*.py"] if config.RELOAD else None

    uvicorn.run(
        "main:app",
        host=config.API_HOST,
        port=config.API_PORT,
        reload=config.RELOAD,
        log_level=config.LOG_LEVEL,
        reload_dirs=reload_dirs,
        reload_includes=reload_includes,
        reload_excludes=[
            ".venv/**",
            "**/.venv/**",
            "__pycache__/**",
            "**/__pycache__/**",
            ".git/**"
        ]
    )
