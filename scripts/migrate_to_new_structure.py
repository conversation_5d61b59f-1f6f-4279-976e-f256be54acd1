#!/usr/bin/env python3
"""
Migration script to help transition from old structure to new structure
"""

import os
import shutil
from pathlib import Path


def backup_old_files():
    """Create backup of old files."""
    backup_dir = Path("backup_old_structure")
    backup_dir.mkdir(exist_ok=True)
    
    old_files = [
        "main.py",
        "run.py", 
        "config.py",
        "theta_service.py",
        "test_models.py",
        "database.py"
    ]
    
    for file in old_files:
        if Path(file).exists():
            shutil.copy2(file, backup_dir / file)
            print(f"✅ Backed up {file}")
    
    print(f"📁 Old files backed up to {backup_dir}")


def update_gitignore():
    """Update .gitignore with proper Python API patterns."""
    gitignore_content = """
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
ThetaTerminal.jar
backup_old_structure/
"""
    
    with open(".gitignore", "w") as f:
        f.write(gitignore_content.strip())
    
    print("✅ Updated .gitignore")


def main():
    """Run the migration."""
    print("🚀 Starting migration to new API structure...")
    print()
    
    # Backup old files
    backup_old_files()
    print()
    
    # Update gitignore
    update_gitignore()
    print()
    
    print("✅ Migration completed!")
    print()
    print("Next steps:")
    print("1. Review the new structure in the app/ directory")
    print("2. Test the new API with: python main_new.py")
    print("3. Run tests with: make test")
    print("4. Install dev dependencies: make install-dev")
    print("5. When satisfied, you can remove the old files from backup_old_structure/")


if __name__ == "__main__":
    main()
