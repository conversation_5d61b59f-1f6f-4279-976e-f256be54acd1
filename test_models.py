#!/usr/bin/env python3
"""
Simple test script to verify Pydantic models work without recursion
"""

from datetime import datetime, date
from typing import List, Optional
from enum import Enum
from pydantic import BaseModel

# Minimal TimeScale enum
class TimeScale(str, Enum):
    one_day = "1d"
    one_hour = "1h"

# Minimal models for testing
class TestRequest(BaseModel):
    asset: str
    start_date: date
    time_scale: TimeScale

class TestPoint(BaseModel):
    timestamp: datetime
    price: float

class TestResponse(BaseModel):
    asset: str
    data: List[TestPoint]

def test_models():
    print("Testing Pydantic models...")
    
    # Test basic model creation
    request = TestRequest(
        asset="AAPL",
        start_date=date(2024, 1, 1),
        time_scale=TimeScale.one_day
    )
    print(f"✅ Request model: {request.asset}")
    
    # Test nested model
    point = TestPoint(
        timestamp=datetime.now(),
        price=150.0
    )
    print(f"✅ Data point model: {point.price}")
    
    # Test response with list
    response = TestResponse(
        asset="AAPL",
        data=[point]
    )
    print(f"✅ Response model: {len(response.data)} points")
    
    print("🎉 All models work correctly!")

if __name__ == "__main__":
    test_models() 