"""
Unit tests for health endpoints
"""

import pytest
from fastapi.testclient import Test<PERSON>lient


def test_root_endpoint(client: TestClient):
    """Test the root endpoint."""
    response = client.get("/api/v1/")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "running"
    assert data["message"] == "ThetaData Historical API"


def test_health_endpoint(client: TestClient):
    """Test the health check endpoint."""
    response = client.get("/api/v1/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "timestamp" in data
    assert "theta_terminal_running" in data
