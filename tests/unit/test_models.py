"""
Unit tests for Pydantic models
"""

import pytest
from datetime import datetime, date
from typing import List

from app.models.enums import TimeScale
from app.models.requests import HistoricalDataRequest, OptionsChainRequest
from app.models.responses import HistoricalDataResponse, OptionsChainResponse


class TestHistoricalDataModels:
    """Test historical data request/response models"""
    
    def test_historical_data_request_creation(self):
        """Test creating a historical data request"""
        request = HistoricalDataRequest(
            symbol="AAPL",
            start_date=date(2024, 1, 1),
            end_date=date(2024, 1, 31),
            time_scale=TimeScale.ONE_DAY
        )
        
        assert request.symbol == "AAPL"
        assert request.start_date == date(2024, 1, 1)
        assert request.end_date == date(2024, 1, 31)
        assert request.time_scale == TimeScale.ONE_DAY
    
    def test_historical_data_request_validation(self):
        """Test historical data request validation"""
        # Test that end_date must be after start_date
        with pytest.raises(ValueError):
            HistoricalDataRequest(
                symbol="AAPL",
                start_date=date(2024, 1, 31),
                end_date=date(2024, 1, 1),  # Before start_date
                time_scale=TimeScale.ONE_DAY
            )
    
    def test_historical_data_response_creation(self):
        """Test creating a historical data response"""
        response = HistoricalDataResponse(
            symbol="AAPL",
            data=[
                {
                    "timestamp": datetime(2024, 1, 1, 9, 30),
                    "open": 150.0,
                    "high": 155.0,
                    "low": 149.0,
                    "close": 154.0,
                    "volume": 1000000
                }
            ],
            count=1
        )
        
        assert response.symbol == "AAPL"
        assert len(response.data) == 1
        assert response.count == 1
        assert response.data[0]["close"] == 154.0


class TestOptionsChainModels:
    """Test options chain request/response models"""
    
    def test_options_chain_request_creation(self):
        """Test creating an options chain request"""
        request = OptionsChainRequest(
            symbol="SPY",
            target_datetime=datetime(2024, 1, 15, 15, 30),
            strikes=[400.0, 410.0, 420.0]
        )
        
        assert request.symbol == "SPY"
        assert request.target_datetime == datetime(2024, 1, 15, 15, 30)
        assert request.strikes == [400.0, 410.0, 420.0]
    
    def test_options_chain_response_creation(self):
        """Test creating an options chain response"""
        response = OptionsChainResponse(
            symbol="SPY",
            target_datetime=datetime(2024, 1, 15, 15, 30),
            data=[
                {
                    "strike": 400.0,
                    "call_bid": 10.5,
                    "call_ask": 10.7,
                    "put_bid": 2.1,
                    "put_ask": 2.3
                }
            ],
            count=1
        )
        
        assert response.symbol == "SPY"
        assert len(response.data) == 1
        assert response.count == 1
        assert response.data[0]["strike"] == 400.0


class TestEnums:
    """Test enumeration types"""
    
    def test_time_scale_enum(self):
        """Test TimeScale enum values"""
        assert TimeScale.ONE_MINUTE == "1m"
        assert TimeScale.FIVE_MINUTES == "5m"
        assert TimeScale.ONE_HOUR == "1h"
        assert TimeScale.ONE_DAY == "1d"
    
    def test_time_scale_enum_iteration(self):
        """Test that all TimeScale values are accessible"""
        time_scales = list(TimeScale)
        assert len(time_scales) > 0
        assert TimeScale.ONE_DAY in time_scales
